name: Release Linux Version

on:
  workflow_dispatch:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
    branches-ignore:
      - '*'

jobs:
  release-linux-amd64:
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v4
      - name: Golang Installation
        uses: actions/setup-go@v5
        with:
          go-version: '1.24'
      - name: Golang build
        run: |
          cd src && go build -o linux-amd64
      - name: Deploy artifact to release ${{ github.ref_name }}
        uses: AButler/upload-release-assets@v3.0
        with:
          files: 'src/linux-amd64'
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          release-tag: ${{ github.ref_name }}

  release-linux-arm64:
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: [ARM64]
    environment: production
    steps:
      - uses: actions/checkout@v4
      - name: Golang Installation
        uses: actions/setup-go@v5
        with:
          go-version: '1.24'
      - name: Golang build
        run: |
          cd src && go build -o linux-arm64
      - name: Deploy artifact to release ${{ github.ref_name }}
        uses: AButler/upload-release-assets@v3.0
        with:
          files: 'src/linux-arm64'
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          release-tag: ${{ github.ref_name }}