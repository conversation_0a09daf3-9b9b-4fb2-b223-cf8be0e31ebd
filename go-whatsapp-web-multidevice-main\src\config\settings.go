package config

import (
	"go.mau.fi/whatsmeow/proto/waCompanionReg"
)

var (
	AppVersion               = "v6.0.1"
	AppPort                  = "3000"
	AppDebug                 = false
	AppOs                    = "AldinoKemal"
	AppPlatform              = waCompanionReg.DeviceProps_PlatformType(1)
	AppBasicAuthCredential   []string
	AppChatFlushIntervalDays = 7 // Number of days before flushing chat.csv

	McpPort = "8080"
	McpHost = "localhost"

	PathQrCode      = "statics/qrcode"
	PathSendItems   = "statics/senditems"
	PathMedia       = "statics/media"
	PathStorages    = "storages"
	PathChatStorage = "storages/chat.csv"

	DBURI = "file:storages/whatsapp.db?_foreign_keys=on"

	WhatsappAutoReplyMessage       string
	WhatsappWebhook                []string
	WhatsappWebhookSecret                = "secret"
	WhatsappLogLevel                     = "ERROR"
	WhatsappSettingMaxImageSize    int64 = ********  // 20MB
	WhatsappSettingMaxFileSize     int64 = ********  // 50MB
	WhatsappSettingMaxVideoSize    int64 = ********* // 100MB
	WhatsappSettingMaxDownloadSize int64 = ********* // 500MB
	WhatsappTypeUser                     = "@s.whatsapp.net"
	WhatsappTypeGroup                    = "@g.us"
	WhatsappAccountValidation            = true
	WhatsappChatStorage                  = true
)
