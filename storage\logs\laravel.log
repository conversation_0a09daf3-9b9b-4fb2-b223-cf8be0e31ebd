[2025-05-12 13:25:26] production.ERROR: Database file at path [G:\Laravel\hm-cukup ya\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: delete from "cache") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [G:\\Laravel\\hm-cukup ya\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: delete from \"cache\") at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [G:\\Laravel\\hm-cukup ya\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:59)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(19): Illuminate\\Database\\Connectors\\SQLiteConnector->parseDatabasePath(false)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#2 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(589): Illuminate\\Database\\Connection->getPdo()
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('delete from \"ca...', Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#19 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-05-12 13:25:49] production.ERROR: SQLSTATE[HY000]: General error: 1 no such table: cache (Connection: sqlite, SQL: delete from "cache") {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: cache (Connection: sqlite, SQL: delete from \"cache\") at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: cache at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:589)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(589): PDO->prepare('delete from \"ca...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('delete from \"ca...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#23 {main}
"} 
[2025-05-12 14:06:23] production.ERROR: rename(G:\Laravel\hm-cukup ya\bootstrap\cache\ser3E82.tmp,G:\Laravel\hm-cukup ya\bootstrap\cache\services.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(G:\\Laravel\\hm-cukup ya\\bootstrap\\cache\\ser3E82.tmp,G:\\Laravel\\hm-cukup ya\\bootstrap\\cache\\services.php): Access is denied (code: 5) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('G:\\\\Laravel\\\\hm-c...', 'G:\\\\Laravel\\\\hm-c...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(190): Illuminate\\Filesystem\\Filesystem->replace('G:\\\\Laravel\\\\hm-c...', '<?php return ar...')
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(162): Illuminate\\Foundation\\ProviderRepository->writeManifest(Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#11 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-05-12 15:37:49] production.ERROR: SQLSTATE[HY000]: General error: 1 no such table: cache (Connection: sqlite, SQL: delete from "cache") {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: cache (Connection: sqlite, SQL: delete from \"cache\") at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#21 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: cache at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:589)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(589): PDO->prepare('delete from \"ca...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('delete from \"ca...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Cache\\Console\\ClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#23 {main}
"} 
[2025-05-12 15:38:01] production.ERROR: SQLSTATE[HY000]: General error: 1 no such table: cache (Connection: sqlite, SQL: delete from "cache") {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: cache (Connection: sqlite, SQL: delete from \"cache\") at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(41): Illuminate\\Console\\Command->runCommand('cache:clear', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(53): Illuminate\\Console\\Command->callSilent('cache:clear', Array)
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\OptimizeClearCommand.php(48): Illuminate\\Console\\Command->callSilently('cache:clear')
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Foundation\\Console\\OptimizeClearCommand->Illuminate\\Foundation\\Console\\{closure}()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(59): Illuminate\\Console\\View\\Components\\Task->render('cache', Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\OptimizeClearCommand.php(48): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\OptimizeClearCommand->handle()
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\OptimizeClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: cache at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:589)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(589): PDO->prepare('delete from \"ca...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('delete from \"ca...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('delete from \"ca...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->run('delete from \"ca...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('delete from \"ca...', Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(4037): Illuminate\\Database\\Connection->delete('delete from \"ca...', Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php(421): Illuminate\\Database\\Query\\Builder->delete()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(806): Illuminate\\Cache\\DatabaseStore->flush()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Console\\ClearCommand.php(68): Illuminate\\Cache\\Repository->__call('flush', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Cache\\Console\\ClearCommand->handle()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(41): Illuminate\\Console\\Command->runCommand('cache:clear', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(53): Illuminate\\Console\\Command->callSilent('cache:clear', Array)
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\OptimizeClearCommand.php(48): Illuminate\\Console\\Command->callSilently('cache:clear')
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Foundation\\Console\\OptimizeClearCommand->Illuminate\\Foundation\\Console\\{closure}()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(59): Illuminate\\Console\\View\\Components\\Task->render('cache', Object(Closure))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\OptimizeClearCommand.php(48): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\OptimizeClearCommand->handle()
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\OptimizeClearCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#38 {main}
"} 
[2025-05-13 19:24:17] production.INFO: Starting migration to fix labor_cost in mechanic_service table  
[2025-05-13 19:24:17] production.INFO: Found 0 records in mechanic_service table  
[2025-05-13 19:24:17] production.INFO: Regenerating all mechanic reports  
[2025-05-13 19:24:17] production.INFO: Found 0 reports in mechanic_reports table  
[2025-05-13 19:24:17] production.INFO: Finished regenerating all mechanic reports  
[2025-05-13 19:24:17] production.INFO: Finished migration to fix labor_cost in mechanic_service table  
[2025-05-13 19:30:32] production.ERROR: Declaration of App\Filament\Resources\CustomerResource\RelationManagers\MembershipsRelationManager::canViewForRecord(Illuminate\Database\Eloquent\Model $ownerRecord): bool must be compatible with Filament\Resources\RelationManagers\RelationManager::canViewForRecord(Illuminate\Database\Eloquent\Model $ownerRecord, string $pageClass): bool {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Filament\\Resources\\CustomerResource\\RelationManagers\\MembershipsRelationManager::canViewForRecord(Illuminate\\Database\\Eloquent\\Model $ownerRecord): bool must be compatible with Filament\\Resources\\RelationManagers\\RelationManager::canViewForRecord(Illuminate\\Database\\Eloquent\\Model $ownerRecord, string $pageClass): bool at G:\\Laravel\\hm-cukup ya\\app\\Filament\\Resources\\CustomerResource\\RelationManagers\\MembershipsRelationManager.php:25)
[stacktrace]
#0 {main}
"} 
[2025-05-13 19:49:07] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 G:\\Laravel\\hm-cukup ya\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#49 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('G:\\\\Laravel\\\\hm-c...')
#50 {main}
"} 
[2025-05-13 19:49:07] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 G:\\Laravel\\hm-cukup ya\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('G:\\\\Laravel\\\\hm-c...')
#21 {main}
"} 
[2025-05-13 19:59:25] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 G:\\Laravel\\hm-cukup ya\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('G:\\\\Laravel\\\\hm-c...')
#52 {main}
"} 
[2025-05-13 19:59:26] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 G:\\Laravel\\hm-cukup ya\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('G:\\\\Laravel\\\\hm-c...')
#21 {main}
"} 
[2025-05-13 20:00:09] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\src\\Http\\Middleware\\SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 G:\\Laravel\\hm-cukup ya\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#51 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('G:\\\\Laravel\\\\hm-c...')
#52 {main}
"} 
[2025-05-13 20:00:09] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 G:\\Laravel\\hm-cukup ya\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('G:\\\\Laravel\\\\hm-c...')
#21 {main}
"} 
[2025-05-16 13:13:57] production.ERROR: rename(G:\Laravel\hm-cukup ya\bootstrap\cache\serD292.tmp,G:\Laravel\hm-cukup ya\bootstrap\cache\services.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(G:\\Laravel\\hm-cukup ya\\bootstrap\\cache\\serD292.tmp,G:\\Laravel\\hm-cukup ya\\bootstrap\\cache\\services.php): Access is denied (code: 5) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('G:\\\\Laravel\\\\hm-c...', 'G:\\\\Laravel\\\\hm-c...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(190): Illuminate\\Filesystem\\Filesystem->replace('G:\\\\Laravel\\\\hm-c...', '<?php return ar...')
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(162): Illuminate\\Foundation\\ProviderRepository->writeManifest(Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#11 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-05-16 13:14:13] production.ERROR: Declaration of App\Policies\ServicePolicy::view(App\Models\User $user, App\Models\Service $service): bool must be compatible with App\Policies\BasePolicy::view(App\Models\User $user, $model): bool {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Policies\\ServicePolicy::view(App\\Models\\User $user, App\\Models\\Service $service): bool must be compatible with App\\Policies\\BasePolicy::view(App\\Models\\User $user, $model): bool at G:\\Laravel\\hm-cukup ya\\app\\Policies\\ServicePolicy.php:31)
[stacktrace]
#0 {main}
"} 
[2025-05-16 13:15:12] production.ERROR: Declaration of App\Policies\ServicePolicy::view(App\Models\User $user, App\Models\Service $service): bool must be compatible with App\Policies\BasePolicy::view(App\Models\User $user, $model): bool {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Policies\\ServicePolicy::view(App\\Models\\User $user, App\\Models\\Service $service): bool must be compatible with App\\Policies\\BasePolicy::view(App\\Models\\User $user, $model): bool at G:\\Laravel\\hm-cukup ya\\app\\Policies\\ServicePolicy.php:31)
[stacktrace]
#0 {main}
"} 
[2025-05-16 13:16:13] production.ERROR: Declaration of App\Policies\ServicePolicy::view(App\Models\User $user, App\Models\Service $service): bool must be compatible with App\Policies\BasePolicy::view(App\Models\User $user, $model): bool {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Policies\\ServicePolicy::view(App\\Models\\User $user, App\\Models\\Service $service): bool must be compatible with App\\Policies\\BasePolicy::view(App\\Models\\User $user, $model): bool at G:\\Laravel\\hm-cukup ya\\app\\Policies\\ServicePolicy.php:31)
[stacktrace]
#0 {main}
"} 
[2025-05-16 13:17:13] production.ERROR: Declaration of App\Policies\ServicePolicy::view(App\Models\User $user, App\Models\Service $service): bool must be compatible with App\Policies\BasePolicy::view(App\Models\User $user, $model): bool {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Policies\\ServicePolicy::view(App\\Models\\User $user, App\\Models\\Service $service): bool must be compatible with App\\Policies\\BasePolicy::view(App\\Models\\User $user, $model): bool at G:\\Laravel\\hm-cukup ya\\app\\Policies\\ServicePolicy.php:31)
[stacktrace]
#0 {main}
"} 
[2025-05-16 13:18:12] production.ERROR: Declaration of App\Policies\ServicePolicy::view(App\Models\User $user, App\Models\Service $service): bool must be compatible with App\Policies\BasePolicy::view(App\Models\User $user, $model): bool {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Policies\\ServicePolicy::view(App\\Models\\User $user, App\\Models\\Service $service): bool must be compatible with App\\Policies\\BasePolicy::view(App\\Models\\User $user, $model): bool at G:\\Laravel\\hm-cukup ya\\app\\Policies\\ServicePolicy.php:31)
[stacktrace]
#0 {main}
"} 
[2025-05-16 13:19:12] production.ERROR: Declaration of App\Policies\ServicePolicy::view(App\Models\User $user, App\Models\Service $service): bool must be compatible with App\Policies\BasePolicy::view(App\Models\User $user, $model): bool {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Policies\\ServicePolicy::view(App\\Models\\User $user, App\\Models\\Service $service): bool must be compatible with App\\Policies\\BasePolicy::view(App\\Models\\User $user, $model): bool at G:\\Laravel\\hm-cukup ya\\app\\Policies\\ServicePolicy.php:31)
[stacktrace]
#0 {main}
"} 
[2025-05-16 13:20:13] production.ERROR: Declaration of App\Policies\ServicePolicy::view(App\Models\User $user, App\Models\Service $service): bool must be compatible with App\Policies\BasePolicy::view(App\Models\User $user, $model): bool {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of App\\Policies\\ServicePolicy::view(App\\Models\\User $user, App\\Models\\Service $service): bool must be compatible with App\\Policies\\BasePolicy::view(App\\Models\\User $user, $model): bool at G:\\Laravel\\hm-cukup ya\\app\\Policies\\ServicePolicy.php:31)
[stacktrace]
#0 {main}
"} 
[2025-05-16 13:32:35] production.ERROR: PHP Parse error: Syntax error, unexpected ';', expecting ')' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected ';', expecting ')' on line 1 at G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php var_dump(...', false)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('var_dump(method...', true)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('var_dump(method...', true)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('var_dump(method...')
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-05-16 14:02:56] production.ERROR: Command "livewire:discover" is not defined.

Did you mean one of these?
    livewire:attribute
    livewire:configure-s3-upload-cleanup
    livewire:copy
    livewire:delete
    livewire:form
    livewire:layout
    livewire:make
    livewire:move
    livewire:publish
    livewire:stubs
    livewire:upgrade
    package:discover {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"livewire:discover\" is not defined.

Did you mean one of these?
    livewire:attribute
    livewire:configure-s3-upload-cleanup
    livewire:copy
    livewire:delete
    livewire:form
    livewire:layout
    livewire:make
    livewire:move
    livewire:publish
    livewire:stubs
    livewire:upgrade
    package:discover at G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php:726)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('livewire:discov...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
[2025-05-16 14:03:12] production.ERROR: rename(G:\Laravel\hm-cukup ya\bootstrap\cache\pacEC79.tmp,G:\Laravel\hm-cukup ya\bootstrap\cache/packages.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(G:\\Laravel\\hm-cukup ya\\bootstrap\\cache\\pacEC79.tmp,G:\\Laravel\\hm-cukup ya\\bootstrap\\cache/packages.php): Access is denied (code: 5) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('G:\\\\Laravel\\\\hm-c...', 'G:\\\\Laravel\\\\hm-c...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(182): Illuminate\\Filesystem\\Filesystem->replace('G:\\\\Laravel\\\\hm-c...', '<?php return ar...')
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-05-16 14:16:13] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:16:13] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:13] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:13] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:20] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:20] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:30] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:30] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:44] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:44] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:54] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:17:54] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:18:13] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:18:39] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:18:39] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:19:13] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:19:13] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:19:51] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:19:51] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:19:51] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:19:51] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:19:51] production.ERROR: Uncaught ReflectionException: Class "view" does not exist in G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Container\Container.php:1017
Stack trace:
#0 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Container\Container.php(1017): ReflectionClass->__construct('view')
#1 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Container\Container.php(890): Illuminate\Container\Container->build('view')
#2 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1077): Illuminate\Container\Container->resolve('view', Array, true)
#3 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Container\Container.php(821): Illuminate\Foundation\Application->resolve('view', Array)
#4 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1057): Illuminate\Container\Container->make('view', Array)
#5 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Foundation\helpers.php(127): Illuminate\Foundation\Application->make('view', Array)
#6 Command line code(1): app('view')
#7 {main}

Next Illuminate\Contracts\Container\BindingResolutionException: Target class [view] does not exist. in G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Container\Container.php:1019
Stack trace:
#0 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Container\Container.php(890): Illuminate\Container\Container->build('view')
#1 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1077): Illuminate\Container\Container->resolve('view', Array, true)
#2 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Container\Container.php(821): Illuminate\Foundation\Application->resolve('view', Array)
#3 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(1057): Illuminate\Container\Container->make('view', Array)
#4 G:\Laravel\hm-cukup ya\vendor\laravel\framework\src\Illuminate\Foundation\helpers.php(127): Illuminate\Foundation\Application->make('view', Array)
#5 Command line code(1): app('view')
#6 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"view\" does not exist in G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017
Stack trace:
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('view')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('view')
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('view', Array, true)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('view', Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('view', Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('view', Array)
#6 Command line code(1): app('view')
#7 {main}

Next Illuminate\\Contracts\\Container\\BindingResolutionException: Target class [view] does not exist. in G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019
Stack trace:
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('view')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('view', Array, true)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('view', Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('view', Array)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('view', Array)
#5 Command line code(1): app('view')
#6 {main}
  thrown at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 {main}
"} 
[2025-05-16 14:20:13] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 {main}
"} 
[2025-05-16 14:20:40] production.ERROR: Class "App\Providers\LivewireServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\LivewireServiceProvider\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:205)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(141): Illuminate\\Foundation\\ProviderRepository->createProvider('App\\\\Providers\\\\L...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#9 {main}
"} 
[2025-05-16 14:27:34] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:27:34] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:28:13] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:28:13] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:28:21] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:28:31] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:28:31] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:29:13] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:29:13] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:29:18] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:29:18] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-05-16 14:29:26] production.ERROR: Call to undefined method Livewire\LivewireManager::setUploadRoute() {"exception":"[object] (Error(code: 0): Call to undefined method Livewire\\LivewireManager::setUploadRoute() at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php:361)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\app\\Providers\\LivewireConfigServiceProvider.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('setUploadRoute', Array)
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\LivewireConfigServiceProvider->boot()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\LivewireConfigServiceProvider))
#8 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\LivewireConfigServiceProvider), 'App\\\\Providers\\\\L...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#16 {main}
"} 
[2025-05-16 14:47:55] production.ERROR: Access level to App\Filament\Resources\PromoResource\Pages\CreatePromo::getFooter() must be public (as in class Filament\Pages\Page) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to App\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo::getFooter() must be public (as in class Filament\\Pages\\Page) at G:\\Laravel\\hm-cukup ya\\app\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo.php:24)
[stacktrace]
#0 {main}
"} 
[2025-05-16 14:47:55] production.ERROR: Access level to App\Filament\Resources\PromoResource\Pages\CreatePromo::getFooter() must be public (as in class Filament\Pages\Page) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to App\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo::getFooter() must be public (as in class Filament\\Pages\\Page) at G:\\Laravel\\hm-cukup ya\\app\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo.php:24)
[stacktrace]
#0 {main}
"} 
[2025-05-16 14:47:55] production.ERROR: Access level to App\Filament\Resources\PromoResource\Pages\CreatePromo::getFooter() must be public (as in class Filament\Pages\Page) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to App\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo::getFooter() must be public (as in class Filament\\Pages\\Page) at G:\\Laravel\\hm-cukup ya\\app\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo.php:24)
[stacktrace]
#0 {main}
"} 
[2025-05-16 14:47:55] production.ERROR: Access level to App\Filament\Resources\PromoResource\Pages\CreatePromo::getFooter() must be public (as in class Filament\Pages\Page) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to App\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo::getFooter() must be public (as in class Filament\\Pages\\Page) at G:\\Laravel\\hm-cukup ya\\app\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo.php:24)
[stacktrace]
#0 {main}
"} 
[2025-05-16 14:48:13] production.ERROR: Access level to App\Filament\Resources\PromoResource\Pages\CreatePromo::getFooter() must be public (as in class Filament\Pages\Page) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to App\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo::getFooter() must be public (as in class Filament\\Pages\\Page) at G:\\Laravel\\hm-cukup ya\\app\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo.php:24)
[stacktrace]
#0 {main}
"} 
[2025-05-16 14:48:13] production.ERROR: Access level to App\Filament\Resources\PromoResource\Pages\CreatePromo::getFooter() must be public (as in class Filament\Pages\Page) {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Access level to App\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo::getFooter() must be public (as in class Filament\\Pages\\Page) at G:\\Laravel\\hm-cukup ya\\app\\Filament\\Resources\\PromoResource\\Pages\\CreatePromo.php:24)
[stacktrace]
#0 {main}
"} 
[2025-05-16 15:18:13] production.ERROR: rename(G:\Laravel\hm-cukup ya\bootstrap\cache\pac9968.tmp,G:\Laravel\hm-cukup ya\bootstrap\cache/packages.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(G:\\Laravel\\hm-cukup ya\\bootstrap\\cache\\pac9968.tmp,G:\\Laravel\\hm-cukup ya\\bootstrap\\cache/packages.php): Access is denied (code: 5) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('G:\\\\Laravel\\\\hm-c...', 'G:\\\\Laravel\\\\hm-c...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(182): Illuminate\\Filesystem\\Filesystem->replace('G:\\\\Laravel\\\\hm-c...', '<?php return ar...')
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(132): Illuminate\\Foundation\\PackageManifest->write(Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(108): Illuminate\\Foundation\\PackageManifest->build()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(90): Illuminate\\Foundation\\PackageManifest->getManifest()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\PackageManifest.php(79): Illuminate\\Foundation\\PackageManifest->config('aliases')
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterFacades.php(26): Illuminate\\Foundation\\PackageManifest->aliases()
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterFacades->bootstrap(Object(Illuminate\\Foundation\\Application))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#12 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}
"} 
[2025-05-19 13:49:39] production.ERROR: SQLSTATE[HY000]: General error: 1 table "service_reports" already exists (Connection: sqlite, SQL: create table "service_reports" ("id" integer primary key autoincrement not null, "service_id" integer not null, "title" varchar not null default 'Laporan Digital Paket Napas Baru Premium', "unique_code" varchar not null, "customer_name" varchar not null, "license_plate" varchar not null, "car_model" varchar not null, "technician_name" varchar, "summary" text, "recommendations" text, "warranty_info" text, "services_performed" text, "additional_services" text, "service_date" datetime not null, "expires_at" datetime not null, "is_active" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime, foreign key("service_id") references "services"("id") on delete cascade)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"service_reports\" already exists (Connection: sqlite, SQL: create table \"service_reports\" (\"id\" integer primary key autoincrement not null, \"service_id\" integer not null, \"title\" varchar not null default 'Laporan Digital Paket Napas Baru Premium', \"unique_code\" varchar not null, \"customer_name\" varchar not null, \"license_plate\" varchar not null, \"car_model\" varchar not null, \"technician_name\" varchar, \"summary\" text, \"recommendations\" text, \"warranty_info\" text, \"services_performed\" text, \"additional_services\" text, \"service_date\" datetime not null, \"expires_at\" datetime not null, \"is_active\" tinyint(1) not null default '1', \"created_at\" datetime, \"updated_at\" datetime, foreign key(\"service_id\") references \"services\"(\"id\") on delete cascade)) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"s...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"s...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"s...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(621): Illuminate\\Database\\Schema\\Blueprint->build()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(475): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('service_reports', Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_20_000000_create_service_reports_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_20_0000...', Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_20_0000...', Object(Closure))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 4, false)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"service_reports\" already exists at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"s...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"s...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"s...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"s...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"s...')
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(621): Illuminate\\Database\\Schema\\Blueprint->build()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(475): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('service_reports', Object(Closure))
#8 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_20_000000_create_service_reports_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_20_0000...', Object(Closure))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_20_0000...', Object(Closure))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 4, false)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-05-19 13:49:49] production.ERROR: SQLSTATE[HY000]: General error: 1 table "service_reports" already exists (Connection: sqlite, SQL: create table "service_reports" ("id" integer primary key autoincrement not null, "service_id" integer not null, "title" varchar not null default 'Laporan Digital Paket Napas Baru Premium', "unique_code" varchar not null, "customer_name" varchar not null, "license_plate" varchar not null, "car_model" varchar not null, "technician_name" varchar, "summary" text, "recommendations" text, "warranty_info" text, "services_performed" text, "additional_services" text, "service_date" datetime not null, "expires_at" datetime not null, "is_active" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime, foreign key("service_id") references "services"("id") on delete cascade)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"service_reports\" already exists (Connection: sqlite, SQL: create table \"service_reports\" (\"id\" integer primary key autoincrement not null, \"service_id\" integer not null, \"title\" varchar not null default 'Laporan Digital Paket Napas Baru Premium', \"unique_code\" varchar not null, \"customer_name\" varchar not null, \"license_plate\" varchar not null, \"car_model\" varchar not null, \"technician_name\" varchar, \"summary\" text, \"recommendations\" text, \"warranty_info\" text, \"services_performed\" text, \"additional_services\" text, \"service_date\" datetime not null, \"expires_at\" datetime not null, \"is_active\" tinyint(1) not null default '1', \"created_at\" datetime, \"updated_at\" datetime, foreign key(\"service_id\") references \"services\"(\"id\") on delete cascade)) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"s...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"s...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"s...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(621): Illuminate\\Database\\Schema\\Blueprint->build()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(475): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('service_reports', Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_20_000000_create_service_reports_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_20_0000...', Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_20_0000...', Object(Closure))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 5, false)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"service_reports\" already exists at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"s...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"s...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"s...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"s...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"s...')
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(621): Illuminate\\Database\\Schema\\Blueprint->build()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(475): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('service_reports', Object(Closure))
#8 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_20_000000_create_service_reports_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_20_0000...', Object(Closure))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_20_0000...', Object(Closure))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 5, false)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-05-19 13:56:14] production.ERROR: Class "App\Providers\Filament\ServiceReportResource" not found {"exception":"[object] (Error(code: 0): Class \"App\\Providers\\Filament\\ServiceReportResource\" not found at G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasComponents.php:525)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\src\\Panel.php(69): Filament\\Panel->registerLivewireComponents()
#1 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\src\\PanelRegistry.php(19): Filament\\Panel->register()
#2 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): Filament\\PanelRegistry->register(Object(Filament\\Panel))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1413): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(127): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#10 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('filament', Array, true)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('filament', Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('filament', Array)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1643): Illuminate\\Foundation\\Application->make('filament')
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('filament')
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#21 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('G:\\\\Laravel\\\\hm-c...')
#27 G:\\Laravel\\hm-cukup ya\\vendor\\spatie\\laravel-package-tools\\src\\Concerns\\PackageServiceProvider\\ProcessRoutes.php(14): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('G:\\\\Laravel\\\\hm-c...')
#28 G:\\Laravel\\hm-cukup ya\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(85): Spatie\\LaravelPackageTools\\PackageServiceProvider->bootPackageRoutes()
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#30 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#35 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#36 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#37 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#38 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#39 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#40 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#41 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#42 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 {main}
"} 
[2025-05-19 14:08:49] production.ERROR: SQLSTATE[HY000]: General error: 1 table "service_reports" already exists (Connection: sqlite, SQL: create table "service_reports" ("id" integer primary key autoincrement not null, "service_id" integer not null, "title" varchar not null default 'Laporan Digital Paket Napas Baru Premium', "unique_code" varchar not null, "customer_name" varchar not null, "license_plate" varchar not null, "car_model" varchar not null, "technician_name" varchar, "summary" text, "recommendations" text, "warranty_info" text, "services_performed" text, "additional_services" text, "service_date" datetime not null, "expires_at" datetime not null, "is_active" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime, foreign key("service_id") references "services"("id") on delete cascade)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"service_reports\" already exists (Connection: sqlite, SQL: create table \"service_reports\" (\"id\" integer primary key autoincrement not null, \"service_id\" integer not null, \"title\" varchar not null default 'Laporan Digital Paket Napas Baru Premium', \"unique_code\" varchar not null, \"customer_name\" varchar not null, \"license_plate\" varchar not null, \"car_model\" varchar not null, \"technician_name\" varchar, \"summary\" text, \"recommendations\" text, \"warranty_info\" text, \"services_performed\" text, \"additional_services\" text, \"service_date\" datetime not null, \"expires_at\" datetime not null, \"is_active\" tinyint(1) not null default '1', \"created_at\" datetime, \"updated_at\" datetime, foreign key(\"service_id\") references \"services\"(\"id\") on delete cascade)) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"s...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"s...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"s...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(621): Illuminate\\Database\\Schema\\Blueprint->build()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(475): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('service_reports', Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_20_000000_create_service_reports_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_20_0000...', Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_20_0000...', Object(Closure))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 7, false)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"service_reports\" already exists at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"s...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"s...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"s...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"s...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"s...')
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(621): Illuminate\\Database\\Schema\\Blueprint->build()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(475): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('service_reports', Object(Closure))
#8 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_20_000000_create_service_reports_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_20_0000...', Object(Closure))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_20_0000...', Object(Closure))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 7, false)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-05-19 14:10:14] production.ERROR: rename(G:\Laravel\hm-cukup ya\bootstrap\cache\serD913.tmp,G:\Laravel\hm-cukup ya\bootstrap\cache/services.php): Access is denied (code: 5) {"exception":"[object] (ErrorException(code: 0): rename(G:\\Laravel\\hm-cukup ya\\bootstrap\\cache\\serD913.tmp,G:\\Laravel\\hm-cukup ya\\bootstrap\\cache/services.php): Access is denied (code: 5) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:233)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'rename(G:\\\\Larav...', 'G:\\\\Laravel\\\\hm-c...', 233)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(233): rename('G:\\\\Laravel\\\\hm-c...', 'G:\\\\Laravel\\\\hm-c...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(190): Illuminate\\Filesystem\\Filesystem->replace('G:\\\\Laravel\\\\hm-c...', '<?php return ar...')
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(162): Illuminate\\Foundation\\ProviderRepository->writeManifest(Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(60): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(870): Illuminate\\Foundation\\ProviderRepository->load(Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(37): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#11 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-05-23 17:25:31] production.ERROR: SQLSTATE[HY000]: General error: 1 table "service_reports" already exists (Connection: sqlite, SQL: create table "service_reports" ("id" integer primary key autoincrement not null, "service_id" integer not null, "title" varchar not null default 'Laporan Digital Paket Napas Baru Premium', "unique_code" varchar not null, "customer_name" varchar not null, "license_plate" varchar not null, "car_model" varchar not null, "technician_name" varchar, "summary" text, "recommendations" text, "warranty_info" text, "services_performed" text, "additional_services" text, "service_date" datetime not null, "expires_at" datetime not null, "is_active" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime, foreign key("service_id") references "services"("id") on delete cascade)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"service_reports\" already exists (Connection: sqlite, SQL: create table \"service_reports\" (\"id\" integer primary key autoincrement not null, \"service_id\" integer not null, \"title\" varchar not null default 'Laporan Digital Paket Napas Baru Premium', \"unique_code\" varchar not null, \"customer_name\" varchar not null, \"license_plate\" varchar not null, \"car_model\" varchar not null, \"technician_name\" varchar, \"summary\" text, \"recommendations\" text, \"warranty_info\" text, \"services_performed\" text, \"additional_services\" text, \"service_date\" datetime not null, \"expires_at\" datetime not null, \"is_active\" tinyint(1) not null default '1', \"created_at\" datetime, \"updated_at\" datetime, foreign key(\"service_id\") references \"services\"(\"id\") on delete cascade)) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"s...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"s...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"s...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(621): Illuminate\\Database\\Schema\\Blueprint->build()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(475): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('service_reports', Object(Closure))
#6 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_20_000000_create_service_reports_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_20_0000...', Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_20_0000...', Object(Closure))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 8, false)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"service_reports\" already exists at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"s...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"s...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"s...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"s...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"s...')
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(621): Illuminate\\Database\\Schema\\Blueprint->build()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(475): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('service_reports', Object(Closure))
#8 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_20_000000_create_service_reports_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_20_0000...', Object(Closure))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_20_0000...', Object(Closure))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 8, false)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-05-26 00:49:30] production.ERROR: SQLSTATE[HY000]: General error: 1 no such function: NOW (Connection: sqlite, SQL: 
            INSERT INTO mechanic_report_archives
            (mechanic_id, week_start, week_end, services_count, total_labor_cost, notes, is_paid, paid_at, archived_at, archive_reason, created_at, updated_at)
            SELECT
                mechanic_id, week_start, week_end, services_count, total_labor_cost, notes, is_paid, paid_at, NOW(), "weekly_to_cumulative_migration", created_at, updated_at
            FROM mechanic_reports
        ) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: NOW (Connection: sqlite, SQL: 
            INSERT INTO mechanic_report_archives
            (mechanic_id, week_start, week_end, services_count, total_labor_cost, notes, is_paid, paid_at, archived_at, archive_reason, created_at, updated_at)
            SELECT
                mechanic_id, week_start, week_end, services_count, total_labor_cost, notes, is_paid, paid_at, NOW(), \"weekly_to_cumulative_migration\", created_at, updated_at
            FROM mechanic_reports
        ) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('\\n            IN...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('\\n            IN...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->statement('\\n            IN...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('statement', Array)
#4 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_26_004503_transform_mechanic_reports_to_cumulative.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('statement', Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_26_0045...', Object(Closure))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_26_0045...', Object(Closure))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 9, false)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: NOW at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('\\n            IN...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('\\n            IN...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('\\n            IN...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('\\n            IN...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->statement('\\n            IN...')
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('statement', Array)
#6 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_26_004503_transform_mechanic_reports_to_cumulative.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('statement', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_26_0045...', Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_26_0045...', Object(Closure))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 9, false)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}
"} 
[2025-05-26 00:50:36] production.ERROR: SQLSTATE[HY000]: General error: 1 no such function: NOW (Connection: sqlite, SQL: 
            INSERT INTO mechanic_report_archives
            (mechanic_id, week_start, week_end, services_count, total_labor_cost, notes, is_paid, paid_at, archived_at, archive_reason, created_at, updated_at)
            SELECT
                mechanic_id, week_start, week_end, services_count, total_labor_cost, notes, is_paid, paid_at, NOW(), "weekly_to_cumulative_migration", created_at, updated_at
            FROM mechanic_reports
        ) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: NOW (Connection: sqlite, SQL: 
            INSERT INTO mechanic_report_archives
            (mechanic_id, week_start, week_end, services_count, total_labor_cost, notes, is_paid, paid_at, archived_at, archive_reason, created_at, updated_at)
            SELECT
                mechanic_id, week_start, week_end, services_count, total_labor_cost, notes, is_paid, paid_at, NOW(), \"weekly_to_cumulative_migration\", created_at, updated_at
            FROM mechanic_reports
        ) at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('\\n            IN...', Array, Object(Closure))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('\\n            IN...', Array, Object(Closure))
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->statement('\\n            IN...')
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('statement', Array)
#4 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_26_004503_transform_mechanic_reports_to_cumulative.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('statement', Array)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_26_0045...', Object(Closure))
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_26_0045...', Object(Closure))
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 10, false)
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such function: NOW at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('\\n            IN...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('\\n            IN...', Array)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('\\n            IN...', Array, Object(Closure))
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('\\n            IN...', Array, Object(Closure))
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(495): Illuminate\\Database\\Connection->statement('\\n            IN...')
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\DatabaseManager->__call('statement', Array)
#6 G:\\Laravel\\hm-cukup ya\\database\\migrations\\2025_05_26_004503_transform_mechanic_reports_to_cumulative.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('statement', Array)
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_05_26_0045...', Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_05_26_0045...', Object(Closure))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('G:\\\\Laravel\\\\hm-c...', 10, false)
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}
"} 
[2025-05-26 01:07:49] production.ERROR: Unable to locate a class or view for component [filament::input.label]. {"exception":"[object] (InvalidArgumentException(code: 0): Unable to locate a class or view for component [filament::input.label]. at G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php:315)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(235): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentClass('filament::input...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(156): Illuminate\\View\\Compilers\\ComponentTagCompiler->componentString('filament::input...', Array)
#2 [internal function]: Illuminate\\View\\Compilers\\ComponentTagCompiler->Illuminate\\View\\Compilers\\{closure}(Array)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(151): preg_replace_callback('/\\n            <...', Object(Closure), '<x-filament::se...')
#4 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(90): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileOpeningTags('<x-filament::se...')
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\ComponentTagCompiler.php(76): Illuminate\\View\\Compilers\\ComponentTagCompiler->compileTags('<x-filament::se...')
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(450): Illuminate\\View\\Compilers\\ComponentTagCompiler->compile('<x-filament::se...')
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(282): Illuminate\\View\\Compilers\\BladeCompiler->compileComponentTags('<x-filament::se...')
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\BladeCompiler.php(186): Illuminate\\View\\Compilers\\BladeCompiler->compileString('<x-filament::se...')
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ViewCacheCommand.php(64): Illuminate\\View\\Compilers\\BladeCompiler->compile('G:\\\\Laravel\\\\hm-c...')
#10 [internal function]: Illuminate\\Foundation\\Console\\ViewCacheCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Symfony\\Component\\Finder\\SplFileInfo), 'G:\\\\Laravel\\\\hm-c...')
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(679): array_map(Object(Closure), Array, Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(800): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ViewCacheCommand.php(61): Illuminate\\Support\\Collection->map(Object(Closure))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ViewCacheCommand.php(43): Illuminate\\Foundation\\Console\\ViewCacheCommand->compileViews(Object(Illuminate\\Support\\Collection))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(274): Illuminate\\Foundation\\Console\\ViewCacheCommand->Illuminate\\Foundation\\Console\\{closure}('G:\\\\Laravel\\\\hm-c...', 0)
#16 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ViewCacheCommand.php(38): Illuminate\\Support\\Collection->each(Object(Closure))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\ViewCacheCommand->handle()
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#24 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#25 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#26 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(41): Illuminate\\Console\\Command->runCommand('view:cache', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#27 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(53): Illuminate\\Console\\Command->callSilent('view:cache', Array)
#28 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\OptimizeCommand.php(48): Illuminate\\Console\\Command->callSilently('view:cache')
#29 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Foundation\\Console\\OptimizeCommand->Illuminate\\Foundation\\Console\\{closure}()
#30 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(59): Illuminate\\Console\\View\\Components\\Task->render('views', Object(Closure))
#31 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\OptimizeCommand.php(48): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#32 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\OptimizeCommand->handle()
#33 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#34 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#35 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#36 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#37 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#38 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\OptimizeCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#46 {main}
"} 
[2025-05-26 02:39:40] production.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 3 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 3 at G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php \\nuse Illu...', false)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('\\nuse Illuminate...', true)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('\\nuse Illuminate...', true)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('\\nuse Illuminate...')
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-05-26 02:40:58] production.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('table', 'mechanic_report...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--table=mechani...')
#2 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--table=mechani...', true)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-05-26 02:50:49] production.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('table', 'mechanic_report...')
#1 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--table=mechani...')
#2 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--table=mechani...', true)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-05-26 11:58:57] production.ERROR: Unclosed '{' on line 12 {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Unclosed '{' on line 12 at G:\\Laravel\\hm-cukup ya\\app\\Models\\MechanicRating.php:56)
[stacktrace]
#0 {main}
"} 
[2025-05-27 01:44:57] production.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 3 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 3 at G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php \\n// Creat...', false)
#2 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('\\n// Create some...', true)
#4 G:\\Laravel\\hm-cukup ya\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('\\n// Create some...', true)
#5 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('\\n// Create some...')
#6 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 G:\\Laravel\\hm-cukup ya\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 G:\\Laravel\\hm-cukup ya\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 G:\\Laravel\\hm-cukup ya\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Starting cumulative mechanic reports synchronization  
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Validating and updating all cumulative reports  
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Found 0 cumulative reports to validate  
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Found 3 active mechanics  
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Created cumulative report for mechanic #1  
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Created cumulative report for mechanic #2  
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Created cumulative report for mechanic #3  
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Rebuild completed: 3 created, 0 updated, 0 errors  
[2025-05-28 06:01:02] production.INFO: SyncMechanicReports: Cumulative mechanic reports synchronization completed  
