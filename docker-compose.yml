services:
  # PHP Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hartono-app
    restart: unless-stopped
    volumes:
      - ./:/var/www/html
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
      - ./public/build:/var/www/html/public/build
    networks:
      - hartono-network
    depends_on:
      - db
    user: root

  # Nginx Web Server
  webserver:
    image: nginx:alpine
    container_name: hartono-webserver
    restart: unless-stopped
    ports:
      - "80:80"
      - "8443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/conf.d/:/etc/nginx/conf.d/
      - ./docker/nginx/ssl/:/etc/nginx/ssl/
      - ./docker/certbot/conf:/etc/letsencrypt
      - ./docker/certbot/www:/var/www/certbot
    networks:
      - hartono-network
    depends_on:
      - app

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: hartono-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - hartono-network

  # PhpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: hartono-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: ${DB_ROOT_PASSWORD}
      UPLOAD_LIMIT: 300M
    ports:
      - "8080:80"
    networks:
      - hartono-network
    depends_on:
      - db

  # WhatsApp API Server
  whatsapp-api:
    build:
      context: ./go-whatsapp-web-multidevice-main
      dockerfile: ./docker/golang.Dockerfile
    container_name: hartono-whatsapp-api
    restart: unless-stopped
    ports:
      - "0.0.0.0:3000:3000"
    environment:
      - APP_PORT=3000
      - APP_DEBUG=false
      - APP_OS=Chrome
      - APP_BASIC_AUTH=${WHATSAPP_BASIC_AUTH:-}
      - DB_URI=file:storages/whatsapp.db?_foreign_keys=on
      - WHATSAPP_WEBHOOK=${WHATSAPP_WEBHOOK:-}
      - WHATSAPP_WEBHOOK_SECRET=${WHATSAPP_WEBHOOK_SECRET:-secret}
      - WHATSAPP_AUTO_REPLY=${WHATSAPP_AUTO_REPLY:-}
      - WHATSAPP_ACCOUNT_VALIDATION=true
      - WHATSAPP_CHAT_STORAGE=true
    volumes:
      - whatsapp_data:/app/storages
      - whatsapp_statics:/app/statics
    networks:
      hartono-network:
        aliases:
          - whatsapp-api
          - whatsapp
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000/app/devices"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Certbot for SSL
  certbot:
    image: certbot/certbot
    container_name: hartono-certbot
    volumes:
      - ./docker/certbot/conf:/etc/letsencrypt
      - ./docker/certbot/www:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email --force-renewal -d hartonomotor.xyz -d www.hartonomotor.xyz

networks:
  hartono-network:
    driver: bridge

volumes:
  dbdata:
    driver: local
  whatsapp_data:
    driver: local
  whatsapp_statics:
    driver: local
