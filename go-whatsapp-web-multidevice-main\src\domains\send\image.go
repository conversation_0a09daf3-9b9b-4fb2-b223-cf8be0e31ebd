package send

import "mime/multipart"

type ImageRequest struct {
	Phone       string                `json:"phone" form:"phone"`
	Caption     string                `json:"caption" form:"caption"`
	Image       *multipart.FileHeader `json:"image" form:"image"`
	ImageURL    *string               `json:"image_url" form:"image_url"`
	ViewOnce    bool                  `json:"view_once" form:"view_once"`
	Compress    bool                  `json:"compress"`
	IsForwarded bool                  `json:"is_forwarded" form:"is_forwarded"`
}
